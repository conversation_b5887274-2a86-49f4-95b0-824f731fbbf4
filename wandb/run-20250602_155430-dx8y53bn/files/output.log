Process 0: Loading model and tokenizer for max_length=9064
Process 0: DeepSpeed mode - quantization disabled for compatibility
Process 0: Tokenizer loaded with vocab size: 128260
Process 0: Loading model from meta-llama/Meta-Llama-3.1-8B
Loading checkpoint shards: 100%|█████████████████████████████████████████████████████████████████████████████████| 4/4 [00:00<00:00, 126.31it/s]
Process 0: Resizing token embeddings from 128256 to 128260
The new embeddings will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new lm_head weights will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
Process 0: Model loaded on CPU, DeepSpeed will handle device placement
Process 0: Passed model loading barrier

=== GPU Memory Usage After Model Loading ===
GPU 0: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 1: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 2: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 3: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
==================================================
Process 0: Setting up LoRA for max_length=9064
Process 0: Using LoRA target modules: ['q_proj', 'k_proj', 'v_proj', 'o_proj', 'gate_proj', 'up_proj', 'down_proj']
Process 0: LoRA setup complete, DeepSpeed will handle gradient checkpointing
trainable params: 41,943,040 || all params: 8,072,237,056 || trainable%: 0.5196
Process 0: LoRA model prepared for DeepSpeed initialization
Process 0: Passed LoRA setup barrier
Process 0: Preparing multilingual dataset junkim100/multilingual_instruction_tuning_lima_bactrian with max_length=9064
Process 0: Supported languages: ['de', 'es', 'fr', 'it']
Process 0: Dataset loaded - Train: 54437, Val: 6805, Test: 6805
Tokenizing dataset:   0%|                                                                                                                                                                                                                                  | 0/54437 [00:00<?, ? examples/s]Token indices sequence length is longer than the specified maximum sequence length for this model (9079 > 9064). Running this sequence through the model will result in indexing errors
🔧 Smart truncation applied: preserved </out_translation> ending
📊 Tokenization validation:
   Valid sequences: 1000
   Truncated (smart): 1
   Incomplete (skipped): 0
Tokenizing dataset: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 54437/54437 [01:07<00:00, 808.99 examples/s]
🔧 Smart truncation applied: preserved </out_translation> ending
📊 Tokenization validation:
   Valid sequences: 1000
   Truncated (smart): 1
   Incomplete (skipped): 0
🔧 Smart truncation applied: preserved </out_translation> ending
📊 Tokenization validation:
   Valid sequences: 1000
   Truncated (smart): 1
   Incomplete (skipped): 0
🔧 Smart truncation applied: preserved </out_translation> ending
📊 Tokenization validation:
   Valid sequences: 1000
   Truncated (smart): 1
   Incomplete (skipped): 0
🔧 Smart truncation applied: preserved </out_translation> ending
📊 Tokenization validation:
   Valid sequences: 1000
   Truncated (smart): 1
   Incomplete (skipped): 0
🔧 Smart truncation applied: preserved </out_translation> ending
📊 Tokenization validation:
   Valid sequences: 1000
   Truncated (smart): 1
   Incomplete (skipped): 0
🔧 Smart truncation applied: preserved </out_translation> ending
📊 Tokenization validation:
   Valid sequences: 1000
   Truncated (smart): 1
   Incomplete (skipped): 0
Tokenizing dataset: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 6805/6805 [00:08<00:00, 804.46 examples/s]
🔧 Smart truncation applied: preserved </out_translation> ending
📊 Tokenization validation:
   Valid sequences: 1000
   Truncated (smart): 1
   Incomplete (skipped): 0
🔧 Smart truncation applied: preserved </out_translation> ending
📊 Tokenization validation:
   Valid sequences: 1000
   Truncated (smart): 1
   Incomplete (skipped): 0
Tokenizing dataset: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 6805/6805 [00:08<00:00, 807.87 examples/s]
Filter: 100%|████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 54437/54437 [00:18<00:00, 2926.01 examples/s]
Filter: 100%|██████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 6805/6805 [00:02<00:00, 2938.65 examples/s]
Filter: 100%|██████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 6805/6805 [00:02<00:00, 2990.75 examples/s]
Dataset filtering results:
  Train: 54437 -> 54220 (99.6% retained)
  Val: 6805 -> 6780 (99.6% retained)
  Sequence lengths - Min: 51, Max: 9020, Mean: 403.6
  ✅ Sample 0: Complete format with preserved ending
  ✅ Sample 1: Complete format with preserved ending
  ✅ Sample 2: Complete format with preserved ending
  ✅ Sample 3: Complete format with preserved ending
  ✅ Sample 4: Complete format with preserved ending
  ✅ Sample 5: Complete format with preserved ending
  ✅ Sample 6: Complete format with preserved ending
  ✅ Sample 7: Complete format with preserved ending
  ✅ Sample 8: Complete format with preserved ending
  ✅ Sample 9: Complete format with preserved ending
  📊 Validation sample (first 10): 10/10 complete sequences
❌ Process 0: Error during training: string indices must be integers
Traceback (most recent call last):
  File "/data_x/junkim100/projects/translation_it/train.py", line 1001, in train_model
    sample_lengths = [
  File "/data_x/junkim100/projects/translation_it/train.py", line 1002, in <listcomp>
    len(example["input_ids"]) for example in dataset["train"][:100]
TypeError: string indices must be integers
