2025-06-02 16:05:53,739 INFO    MainThread:4113071 [wandb_setup.py:_flush():70] Current SDK version is 0.19.11
2025-06-02 16:05:53,739 INFO    MainThread:4113071 [wandb_setup.py:_flush():70] Configure stats pid to 4113071
2025-06-02 16:05:53,739 INFO    MainThread:4113071 [wandb_setup.py:_flush():70] Loading settings from /mnt/raid6/junkim100/.config/wandb/settings
2025-06-02 16:05:53,739 INFO    MainThread:4113071 [wandb_setup.py:_flush():70] Loading settings from /data_x/junkim100/projects/translation_it/wandb/settings
2025-06-02 16:05:53,739 INFO    MainThread:4113071 [wandb_setup.py:_flush():70] Loading settings from environment variables
2025-06-02 16:05:53,739 INFO    MainThread:4113071 [wandb_init.py:setup_run_log_directory():724] Logging user logs to /data_x/junkim100/projects/translation_it/wandb/run-20250602_160553-6pxo4e0c/logs/debug.log
2025-06-02 16:05:53,739 INFO    MainThread:4113071 [wandb_init.py:setup_run_log_directory():725] Logging internal logs to /data_x/junkim100/projects/translation_it/wandb/run-20250602_160553-6pxo4e0c/logs/debug-internal.log
2025-06-02 16:05:53,739 INFO    MainThread:4113071 [wandb_init.py:init():852] calling init triggers
2025-06-02 16:05:53,739 INFO    MainThread:4113071 [wandb_init.py:init():857] wandb.init called with sweep_config: {}
config: {'model_name': 'meta-llama/Meta-Llama-3.1-8B', 'dataset_name': 'junkim100/multilingual_instruction_tuning_lima_bactrian', 'world_size': 4, 'max_length': 9064, 'chunk_size': 4096, 'overlap_tokens': 512, 'chunks_per_9k_sequence': 3, 'method': 'LoRA + DeepSpeed ZeRO-3 + Sequence Chunking', 'lora_rank': 16, 'lora_alpha': 32, 'lora_dropout': 0.1, 'trainable_params': '42M', 'trainable_percent': '0.52%', 'deepspeed_stage': 3, 'per_device_batch_size': 1, 'gradient_accumulation_steps': 8, 'effective_batch_size': 32, 'learning_rate': 0.0001, 'supported_languages': ['de', 'es', 'fr', 'it'], 'reasoning_tokens': True, 'epochs': 2, 'chat_template_fixed': True, 'multilingual': True, '_wandb': {}}
2025-06-02 16:05:53,739 INFO    MainThread:4113071 [wandb_init.py:init():893] starting backend
2025-06-02 16:05:53,739 INFO    MainThread:4113071 [wandb_init.py:init():897] sending inform_init request
2025-06-02 16:05:53,740 INFO    MainThread:4113071 [backend.py:_multiprocessing_setup():101] multiprocessing start_methods=fork,spawn,forkserver, using: spawn
2025-06-02 16:05:53,740 INFO    MainThread:4113071 [wandb_init.py:init():907] backend started and connected
2025-06-02 16:05:53,742 INFO    MainThread:4113071 [wandb_init.py:init():1005] updated telemetry
2025-06-02 16:05:53,742 INFO    MainThread:4113071 [wandb_init.py:init():1029] communicating run to backend with 90.0 second timeout
2025-06-02 16:05:57,124 WARNING MainThread:4113071 [wandb_init.py:init():1681] [no run ID] interrupted
Traceback (most recent call last):
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/wandb/sdk/wandb_init.py", line 1677, in init
    return wi.init(run_settings, run_config, run_printer)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/wandb/sdk/wandb_init.py", line 1055, in init
    result = wait_with_progress(
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/wandb/sdk/mailbox/wait_with_progress.py", line 24, in wait_with_progress
    return wait_all_with_progress(
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/wandb/sdk/mailbox/wait_with_progress.py", line 87, in wait_all_with_progress
    return asyncio_compat.run(progress_loop_with_timeout)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/wandb/sdk/lib/asyncio_compat.py", line 30, in run
    return future.result()
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/concurrent/futures/_base.py", line 440, in result
    self._condition.wait(timeout)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/threading.py", line 320, in wait
    waiter.acquire()
KeyboardInterrupt
